import subprocess
from multiprocessing import Pool, cpu_count
import os

# 要扫描的路径及其对应的输出文件名
tasks = [
    ("/mnt/y/BdgP", "BdgP.csv"),
    ("/mnt/y/CFA", "CFA.csv"),
    ("/mnt/y/Cost_Center_Report", "Cost_Center_Report.csv"),
    ("/mnt/y/ECS", "ECS.csv"),
    ("/mnt/y/EHW", "EHW.csv"),
    ("/mnt/y/EMD", "EMD.csv"),
    ("/mnt/y/ENG", "ENG.csv"),
    ("/mnt/y/EPM_DATA", "EPM_DATA.csv"),
    ("/mnt/y/ESE", "ESE.csv"),
    ("/mnt/y/EST", "EST.csv"),
    ("/mnt/y/ESW", "ESW.csv"),
    ("/mnt/y/GM_O", "GM_O.csv"),
    ("/mnt/y/HMI", "HMI.csv"),
    ("/mnt/y/HRL", "HRL.csv"),
    ("/mnt/y/HSE", "HSE.csv"),
    ("/mnt/y/HUD", "HUD.csv"),
    ("/mnt/y/ICM", "ICM.csv"),
    ("/mnt/y/ICO", "ICO.csv"),
    ("/mnt/y/LOG", "LOG.csv"),
    ("/mnt/y/MFI", "MFI.csv"),
    ("/mnt/y/MOE", "MOE.csv"),
    ("/mnt/y/MPM", "MPM.csv"),
    ("/mnt/y/MSD", "MSD.csv"),
    ("/mnt/y/PM", "PM.csv"),
    ("/mnt/y/PUR", "PUR.csv"),
    ("/mnt/y/QMM", "QMM.csv"),
    ("/mnt/y/SCAN", "SCAN.csv"),
    ("/mnt/y/SCN", "SCN.csv"),
    ("/mnt/y/TEF", "TEF.csv"),
    ("/mnt/y/Trialorder", "Trialorder.csv"),
]

# 每个任务的执行函数
def run_task(task):
    path, output = task
    try:
        print(f"🚀 开始扫描: {path}")
        result = subprocess.run(
            ["python3", "opt_size_tree.py", path, "--output", output],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        if result.returncode == 0:
            print(f"✅ 完成: {path}")
        else:
            print(f"❌ 错误扫描 {path}:\n{result.stderr}")
    except Exception as e:
        print(f"⚠️ 异常执行 {path}: {e}")

if __name__ == "__main__":
    # 设置进程数（可调整，如cpu_count()或手动设置）
    process_count = min(6, cpu_count())  # 控制最大并发数，避免 I/O 饱和
    print(f"🌟 使用 {process_count} 个并发进程")

    with Pool(process_count) as pool:
        pool.map(run_task, tasks)

    print("🎉 所有任务完成！")

