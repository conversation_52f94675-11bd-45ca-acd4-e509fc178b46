#!/usr/bin/env python3
import os
import argparse
import humanize
import csv
import time
from pathlib import Path

# 文件和目录信息记录 (path: (size, ctime, mtime))
file_infos = {}
dir_infos = {}
errors = []

file_count = 0
dir_count = 0
error_count = 0

DEFAULT_EXCLUDE_DIRS = {'.git', '.svn', '__pycache__', 'node_modules', '.idea', '.vscode'}

def scan_limited_depth(path: Path, root_path: Path, max_depth: int, exclude_dirs: set, current_depth=0):
    global file_count, dir_count, error_count

    if current_depth > max_depth:
        return 0

    try:
        entries = list(path.iterdir())
    except (PermissionError, FileNotFoundError, OSError) as e:
        errors.append((str(path), 0, f"读取目录失败: {e}"))
        error_count += 1
        return 0

    dir_count += 1
    total_dir_size = 0

    for entry in entries:
        try:
            if entry.is_file():
                stat = entry.stat()
                size = stat.st_size
                ctime = stat.st_ctime
                mtime = stat.st_mtime
                file_infos[str(entry)] = (size, ctime, mtime)
                total_dir_size += size
                file_count += 1

                if file_count % 1000 == 0:
                    print(f"📄 已扫描文件数: {file_count}")

            elif entry.is_dir():
                if entry.name in exclude_dirs:
                    continue
                sub_size = scan_limited_depth(entry, root_path, max_depth, exclude_dirs, current_depth + 1)
                total_dir_size += sub_size

        except (PermissionError, FileNotFoundError, OSError) as e:
            errors.append((str(entry), 0, f"访问出错: {e}"))
            error_count += 1

    # 本目录的ctime/mtime
    try:
        stat = path.stat()
        dir_infos[str(path)] = (total_dir_size, stat.st_ctime, stat.st_mtime)
    except Exception as e:
        errors.append((str(path), 0, f"目录stat出错: {e}"))
        error_count += 1

    if dir_count % 20 == 0:
        print(f"📁 正在扫描目录: {path}（深度: {current_depth}）")

    return total_dir_size

def save_results(output_file):
    print(f"\n💾 正在保存结果到 {output_file}...")
    with open(output_file, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.writer(f)
        writer.writerow(["路径", "类型", "大小", "创建日期", "修改日期", "备注"])

        for path, (size, ctime, mtime) in file_infos.items():
            writer.writerow([
                path,
                "文件",
                humanize.naturalsize(size),
                time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(ctime)),
                time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(mtime)),
                ""
            ])

        for path, (size, ctime, mtime) in dir_infos.items():
            writer.writerow([
                path,
                "目录",
                humanize.naturalsize(size),
                time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(ctime)),
                time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(mtime)),
                ""
            ])

        for path, size, note in errors:
            writer.writerow([path, "错误", "0 B", "", "", note])

    print("✅ 保存完毕！")

def main():
    parser = argparse.ArgumentParser(description="扫描文件大小，限制最大路径深度")
    parser.add_argument("path", help="扫描起始路径")
    parser.add_argument("--output", default="depth_limited_scan.csv", help="输出 CSV 文件名")
    parser.add_argument("--max-depth", type=int, default=3, help="最大扫描深度（起始路径为0）")
    parser.add_argument("--exclude", nargs='*', default=list(DEFAULT_EXCLUDE_DIRS),
                        help="排除的目录名（默认：.git .svn node_modules 等）")
    args = parser.parse_args()

    root_path = Path(args.path)
    if not root_path.exists():
        print(f"❌ 路径不存在: {root_path}")
        return

    print(f"🚀 扫描启动时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔍 最大扫描深度: {args.max_depth}")
    print(f"🚫 排除目录: {', '.join(args.exclude)}\n")

    start_time = time.time()
    scan_limited_depth(root_path, root_path, args.max_depth, set(args.exclude))
    elapsed = time.time() - start_time

    print(f"\n✅ 扫描完成，总用时：{elapsed:.2f} 秒")
    print(f"📊 文件数: {file_count}, 目录数: {dir_count}, 错误项: {len(errors)}")
    save_results(args.output)

    print(f"📁 输出文件: {args.output}")
    print(f"🏁 扫描结束时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")

if __name__ == "__main__":
    main()

