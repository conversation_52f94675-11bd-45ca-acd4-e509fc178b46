import os
import csv
import re

def convert_to_kb(size_str):
    size_str = size_str.strip()
    if size_str == "" or size_str.lower() == "0 bytes" or size_str.lower() == "0 b":
        return 0.0

    match = re.match(r"([\d.]+)\s*(bytes|b|kb|mb|gb)", size_str.strip().lower())
    if not match:
        return 0.0  # 无法解析的默认返回 0

    number, unit = match.groups()
    number = float(number)

    if unit == "bytes" or unit == "b":
        return number / 1024
    elif unit == "kb":
        return number
    elif unit == "mb":
        return number * 1024
    elif unit == "gb":
        return number * 1024 * 1024
    else:
        return 0.0

def process_csv_file(file_path):
    rows = []
    with open(file_path, mode='r', encoding='utf-8-sig') as csv_file:
        reader = csv.reader(csv_file)
        headers = next(reader)
        rows = list(reader)

    # 添加新列名
    new_headers = headers + ['size(kB)']
    new_rows = []

    for row in rows:
        if len(row) < 3:
            continue
        size_str = row[2]
        size_kb = convert_to_kb(size_str)
        new_row = row + [size_kb]
        new_rows.append(new_row)

    # 按 size(kB) 排序（从大到小）
    sorted_rows = sorted(new_rows, key=lambda x: x[-1], reverse=True)

    # 写入新文件
    base_name = os.path.basename(file_path)
    dir_name = os.path.dirname(file_path)
    output_file = os.path.join(dir_name, f"sort_{base_name}")

    with open(output_file, mode='w', encoding='utf-8-sig', newline='') as f:
        writer = csv.writer(f)
        writer.writerow(new_headers)
        writer.writerows(sorted_rows)

    print(f"已处理并生成：{output_file}")

def main():
    for file_name in os.listdir('.'):
        if file_name.endswith('.csv'):
            process_csv_file(file_name)

if __name__ == "__main__":
    main()

